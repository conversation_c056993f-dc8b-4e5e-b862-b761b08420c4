#!/bin/bash

# 图片HTTP服务器部署脚本
# 用于在目标服务器上部署和启动图片服务

echo "=========================================="
echo "图片HTTP服务器部署脚本"
echo "=========================================="

# 检查Python版本
echo "检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
echo "检查pip..."
pip3 --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到pip3，请先安装pip3"
    exit 1
fi

# 安装依赖
echo "安装Python依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 依赖包安装失败"
    exit 1
fi

# 检查图片目录
IMAGES_DIR="/home/<USER>/llm_project/images"
echo "检查图片目录: $IMAGES_DIR"
if [ ! -d "$IMAGES_DIR" ]; then
    echo "警告: 图片目录不存在，将使用测试目录"
    mkdir -p ./test_images
    echo "已创建测试目录: ./test_images"
    echo "请将图片文件放入此目录"
fi

# 设置权限
echo "设置文件权限..."
chmod +x production_start.py
chmod +x start_server.py

# 显示部署信息
echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "启动命令:"
echo "  开发环境: python3 start_server.py"
echo "  生产环境: python3 production_start.py"
echo ""
echo "服务地址: http://**************:23333"
echo "API文档: http://**************:23333/docs"
echo "图片目录: $IMAGES_DIR"
echo ""
echo "示例图片访问URL:"
echo "  http://**************:23333/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c"
echo "  http://**************:23333/api/images"
echo "=========================================="

# 询问是否立即启动
read -p "是否立即启动生产环境服务器? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "启动生产环境服务器..."
    python3 production_start.py
fi
