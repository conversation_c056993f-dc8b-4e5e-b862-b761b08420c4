"""
图片服务器配置文件
"""

import os
from pathlib import Path

class Config:
    """服务器配置类"""
    
    # 服务器配置
    HOST = os.getenv("IMAGE_SERVER_HOST", "**************")
    PORT = int(os.getenv("IMAGE_SERVER_PORT", "23333"))
    
    # 图片目录配置
    IMAGES_DIR = os.getenv("IMAGES_DIR", "/home/<USER>/llm_project/images")
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "image_server.log")
    
    # 安全配置
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg'}
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "50")) * 1024 * 1024  # 50MB
    
    # CORS配置
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")
    
    # 缓存配置
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    CACHE_MAX_AGE = int(os.getenv("CACHE_MAX_AGE", "3600"))  # 1小时
    
    @classmethod
    def get_images_directory(cls) -> Path:
        """获取图片目录路径"""
        images_dir = Path(cls.IMAGES_DIR)
        
        # 如果指定目录不存在，使用当前目录下的test_images
        if not images_dir.exists():
            test_dir = Path("./test_images")
            test_dir.mkdir(exist_ok=True)
            return test_dir
        
        return images_dir
    
    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        
        if cls.PORT < 1 or cls.PORT > 65535:
            errors.append(f"端口号无效: {cls.PORT}")
        
        if not cls.HOST:
            errors.append("主机地址不能为空")
        
        if errors:
            raise ValueError("配置错误: " + "; ".join(errors))
        
        return True

# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    HOST = "127.0.0.1"
    LOG_LEVEL = "DEBUG"

# 生产环境配置
class ProductionConfig(Config):
    """生产环境配置"""
    LOG_LEVEL = "WARNING"
    ENABLE_CACHE = True

# 根据环境变量选择配置
def get_config():
    """获取当前环境配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    else:
        return DevelopmentConfig()
