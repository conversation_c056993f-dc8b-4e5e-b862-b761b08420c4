#!/usr/bin/env python3
"""
生产环境启动脚本
用于在目标服务器 **************:23333 上启动图片服务
"""

import os
import sys
import logging
from pathlib import Path

# 设置生产环境变量
os.environ["ENVIRONMENT"] = "production"
os.environ["IMAGE_SERVER_HOST"] = "**************"
os.environ["IMAGE_SERVER_PORT"] = "23333"
os.environ["IMAGES_DIR"] = "/home/<USER>/llm_project/images"
os.environ["LOG_LEVEL"] = "INFO"

try:
    import uvicorn
    from fastapi import FastAPI
except ImportError:
    print("错误: 缺少必要的依赖包")
    print("请运行: pip install -r requirements.txt")
    sys.exit(1)

from config import get_config
from image_server import app, image_service

def setup_production_logging(config):
    """设置生产环境日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def ensure_images_directory():
    """确保图片目录存在"""
    images_dir = Path("/home/<USER>/llm_project/images")
    if not images_dir.exists():
        print(f"警告: 图片目录不存在: {images_dir}")
        print("请确保目录存在或将图片放在当前目录的 test_images 文件夹中")
        
        # 创建测试目录
        test_dir = Path("./test_images")
        test_dir.mkdir(exist_ok=True)
        print(f"已创建测试目录: {test_dir.absolute()}")
        return test_dir
    
    return images_dir

def main():
    """主函数"""
    config = get_config()
    
    try:
        config.validate_config()
    except ValueError as e:
        print(f"配置错误: {e}")
        sys.exit(1)
    
    setup_production_logging(config)
    logger = logging.getLogger(__name__)
    
    # 检查图片目录
    images_dir = ensure_images_directory()
    
    logger.info("=" * 60)
    logger.info("图片HTTP服务器 - 生产环境启动")
    logger.info("=" * 60)
    logger.info(f"服务地址: http://{config.HOST}:{config.PORT}")
    logger.info(f"图片目录: {images_dir}")
    logger.info(f"API文档: http://{config.HOST}:{config.PORT}/docs")
    logger.info(f"健康检查: http://{config.HOST}:{config.PORT}/health")
    logger.info("=" * 60)
    
    # 显示重要信息
    print(f"\n🚀 图片HTTP服务器启动中...")
    print(f"📍 服务地址: http://{config.HOST}:{config.PORT}")
    print(f"📁 图片目录: {images_dir}")
    print(f"📚 API文档: http://{config.HOST}:{config.PORT}/docs")
    print(f"❤️  健康检查: http://{config.HOST}:{config.PORT}/health")
    
    print(f"\n📋 主要API端点:")
    print(f"   GET  /images/{{filename}}     - 获取图片文件")
    print(f"   GET  /api/images/{{filename}} - 获取图片信息")
    print(f"   GET  /api/images             - 列出所有图片")
    
    print(f"\n🌐 示例访问URL:")
    print(f"   http://{config.HOST}:{config.PORT}/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c")
    print(f"   http://{config.HOST}:{config.PORT}/api/images")
    
    print(f"\n按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        uvicorn.run(
            "image_server:app",
            host=config.HOST,
            port=config.PORT,
            log_level=config.LOG_LEVEL.lower(),
            access_log=True,
            reload=False,
            workers=1  # 生产环境可以增加worker数量
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
        print("\n👋 服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
